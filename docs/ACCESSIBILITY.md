# 校园移动应用适老化功能说明

## 功能概述

本适老化功能基于您提供的参考设计，为校园移动应用提供了完整的适老化支持，包括主题切换、字体放大、语音播报和护眼模式等功能。

## 主要功能

### 1. 主题切换
- **默认主题**: 保持原有的应用外观
- **深蓝白主题**: 高对比度蓝色主题，适合视力较弱的用户
- **巧克力色主题**: 温暖的棕色主题，减少视觉疲劳
- **黑底白字主题**: 最高对比度主题，适合严重视力障碍用户
- **森林绿主题**: 护眼绿色主题，适合长时间使用

### 2. 字体大小调节
- **保持原状**: 使用系统默认字体大小
- **适中**: 字体稍微放大（1.1倍）
- **较大**: 字体明显放大（1.25倍）
- **特大**: 字体大幅放大（1.5倍），适合视力较弱的用户

### 3. 语音播报功能
- **智能语音合成**: 基于 SpeechSynthesis API
- **语速调节**: 支持 0.1-2.0 倍速调节，默认 0.8 倍（适老化）
- **音量控制**: 支持 0-100% 音量调节，默认 100%（适老化）
- **点击播报**: 点击任意内容即可语音播报
- **自动播报**: 重要提示自动语音播报

### 4. 护眼模式
- **蓝光过滤**: 减少屏幕蓝光，保护视力
- **色彩调节**: 柔化色彩，减少视觉刺激

## 技术实现

### 核心文件结构
```
src/
├── store/modules/accessibility.store.ts    # 状态管理
├── utils/speechSynthesis.ts               # 语音合成工具
├── assets/styles/accessibility/themes.styl # 主题样式
├── components/accessibility/              # 组件
│   ├── AccessibilitySettings.vue         # 设置面板
│   └── AccessibilityButton.vue           # 悬浮按钮
├── plugins/accessibility.ts              # 插件初始化
└── views/accessibility/demo.vue          # 演示页面
```

### 浏览器兼容性

#### SpeechSynthesis API 支持情况
- ✅ **iOS Safari 7+**: 完全支持
- ✅ **Android Chrome 33+**: 完全支持
- ✅ **Samsung Internet**: 完全支持
- ✅ **Firefox Mobile**: 完全支持
- ❌ **微信浏览器**: 可能受限，建议提供降级方案

#### 移动端特殊考虑
1. **用户交互触发**: 移动端需要用户手势触发语音
2. **微信浏览器限制**: 可能不支持 SpeechSynthesis API
3. **性能优化**: 避免频繁创建语音对象

## 使用方法

### 1. 基础集成

在应用中添加适老化按钮：
```vue
<template>
  <div class="app">
    <!-- 您的应用内容 -->
    
    <!-- 适老化悬浮按钮 -->
    <AccessibilityButton />
  </div>
</template>

<script>
import AccessibilityButton from '@/components/accessibility/AccessibilityButton.vue';

export default {
  components: {
    AccessibilityButton,
  },
};
</script>
```

### 2. 语音播报使用

#### 全局方法（推荐）
```javascript
// 播报文本
this.$speak('欢迎使用校园移动应用');

// 播报元素内容
this.$speakElement(this.$refs.content);

// 播报按钮操作
this.$speakButton('提交');

// 播报错误信息
this.$speakError('请填写必填字段');

// 播报成功信息
this.$speakSuccess('保存成功');
```

#### 指令使用
```vue
<template>
  <!-- 点击语音播报 -->
  <div v-speak="'这是要播报的内容'">点击我播报</div>
  
  <!-- 自动播报（延迟1秒） -->
  <div v-auto-speak.delay="'重要提示信息'">重要提示</div>
</template>
```

#### 直接调用
```javascript
import speechManager from '@/utils/speechSynthesis';

// 基础播报
await speechManager.speakText('要播报的内容');

// 带参数播报
await speechManager.speakText('要播报的内容', {
  rate: 0.8,    // 语速
  volume: 1.0,  // 音量
  pitch: 1.0,   // 音调
});
```

### 3. 主题和字体使用

#### 获取当前设置
```javascript
import accessibilityStore from '@/store/modules/accessibility.store';

// 获取当前主题
const currentTheme = accessibilityStore.currentTheme;

// 获取当前字体设置
const currentFontSize = accessibilityStore.currentFontSize;

// 检查语音是否开启
const speechEnabled = accessibilityStore.speechEnabled;
```

#### 程序化设置
```javascript
// 切换主题
accessibilityStore.updateTheme('dark-blue');

// 调整字体大小
accessibilityStore.updateFontSize('large');

// 更新语音设置
accessibilityStore.updateSpeechSettings({
  enabled: true,
  rate: 0.8,
  volume: 1.0,
});
```

## 演示页面

访问 `/accessibility/demo` 查看完整的功能演示。

## 最佳实践

### 1. 语音播报
- 为重要操作提供语音反馈
- 错误信息应自动播报
- 避免过于频繁的语音提示
- 提供语音开关控制

### 2. 主题设计
- 确保足够的颜色对比度
- 深色主题应避免纯黑色
- 提供多种主题选择
- 考虑色盲用户需求

### 3. 字体设计
- 支持大字体时调整布局
- 增加行高提高可读性
- 重要信息使用更大字体
- 避免过小的字体

### 4. 交互设计
- 增大可点击区域
- 提供清晰的焦点指示
- 支持键盘导航
- 简化操作流程

## 故障排除

### 语音功能不工作
1. 检查浏览器是否支持 SpeechSynthesis API
2. 确认用户已开启语音功能
3. 检查是否在微信浏览器中（可能不支持）
4. 确保有用户交互触发

### 主题不生效
1. 检查 CSS 样式是否正确加载
2. 确认主题类名是否正确应用
3. 检查 CSS 优先级问题

### 字体大小不变
1. 检查字体缩放类名是否应用
2. 确认 CSS 变量是否正确设置
3. 检查组件是否使用了固定字体大小

## 技术支持

如有问题，请检查：
1. 浏览器控制台是否有错误信息
2. 适老化功能状态：`this.$accessibility.getStatus()`
3. 语音支持检查：`this.$accessibility.checkSupport()`

## 更新日志

### v1.0.0 (2025-01-23)
- ✅ 完成基础适老化功能
- ✅ 支持5种主题切换
- ✅ 支持4级字体大小调节
- ✅ 集成语音合成功能
- ✅ 添加护眼模式
- ✅ 数据持久化存储
- ✅ 移动端适配
- ✅ 微信浏览器兼容性检测
