<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import AccessibilityButton from '@/components/accessibility/AccessibilityButton.vue';

@Component({
  components: {
    AccessibilityButton,
  },
})
export default class LayoutDefault extends Vue {}
</script>

<template lang="pug">
.layout-default
  slot
  AccessibilityButton
</template>

<style lang="stylus" scoped>
.layout-default
  overflow auto
  width 100%
  height 100%
  -webkit-overflow-scrolling touch
</style>
