/**
 * 适老化功能测试工具
 * 用于验证各项功能是否正常工作
 */

import accessibilityStore from '@/store/modules/accessibility.store';
import { speechManager } from './speechSynthesis';

export interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: any;
}

export class AccessibilityTester {
  private results: TestResult[] = [];

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<TestResult[]> {
    this.results = [];

    console.log('🧪 开始适老化功能测试...');

    // 基础功能测试
    await this.testSpeechSynthesisAPI();
    await this.testStoreModule();
    await this.testThemeSystem();
    await this.testFontSizeSystem();
    await this.testDataPersistence();

    // 浏览器兼容性测试
    this.testBrowserCompatibility();

    // 语音功能测试
    await this.testSpeechFeatures();

    console.log('✅ 适老化功能测试完成');
    this.printResults();

    return this.results;
  }

  /**
   * 测试 SpeechSynthesis API
   */
  private async testSpeechSynthesisAPI(): Promise<void> {
    try {
      const supported = speechManager.isApiSupported();
      const voices = speechManager.getVoices();
      const chineseVoices = speechManager.getChineseVoices();

      const recommendedVoice = speechManager.getRecommendedChineseVoice();

      this.addResult({
        name: 'SpeechSynthesis API 支持',
        passed: supported,
        message: supported ? 'API 支持正常' : 'API 不支持',
        details: {
          supported,
          totalVoices: voices.length,
          chineseVoices: chineseVoices.length,
          recommendedVoice: recommendedVoice ? recommendedVoice.name : 'None',
        },
      });
    } catch (error) {
      this.addResult({
        name: 'SpeechSynthesis API 支持',
        passed: false,
        message: `API 测试失败: ${error}`,
      });
    }
  }

  /**
   * 测试 Store 模块
   */
  private async testStoreModule(): Promise<void> {
    try {
      const hasStore = !!accessibilityStore;
      const hasTheme = !!accessibilityStore.theme;

      this.addResult({
        name: 'Store 模块',
        passed: hasStore && hasTheme,
        message: hasStore && hasTheme ? 'Store 模块正常' : 'Store 模块异常',
        details: {
          hasStore,
          hasTheme,
          currentTheme: accessibilityStore.theme,
          currentFontSize: accessibilityStore.fontSize,
          speechEnabled: accessibilityStore.speechEnabled,
        },
      });
    } catch (error) {
      this.addResult({
        name: 'Store 模块',
        passed: false,
        message: `Store 测试失败: ${error}`,
      });
    }
  }

  /**
   * 测试主题系统
   */
  private async testThemeSystem(): Promise<void> {
    try {
      const originalTheme = accessibilityStore.theme;

      // 测试主题切换
      const themes = ['default', 'dark-blue', 'chocolate', 'black-white', 'forest-green'];
      let switchCount = 0;

      themes.forEach(theme => {
        accessibilityStore.updateTheme(theme as any);
        if (accessibilityStore.theme === theme) {
          switchCount += 1;
        }
      });

      // 恢复原主题
      accessibilityStore.updateTheme(originalTheme);

      const passed = switchCount === themes.length;

      this.addResult({
        name: '主题切换系统',
        passed,
        message: passed ? '主题切换正常' : '主题切换异常',
        details: {
          totalThemes: themes.length,
          successfulSwitches: switchCount,
          currentTheme: accessibilityStore.theme,
        },
      });
    } catch (error) {
      this.addResult({
        name: '主题切换系统',
        passed: false,
        message: `主题测试失败: ${error}`,
      });
    }
  }

  /**
   * 测试字体大小系统
   */
  private async testFontSizeSystem(): Promise<void> {
    try {
      const originalFontSize = accessibilityStore.fontSize;

      // 测试字体大小切换
      const fontSizes = ['keep-original', 'medium', 'large', 'extra-large'];
      let switchCount = 0;

      fontSizes.forEach(fontSize => {
        accessibilityStore.updateFontSize(fontSize as any);
        if (accessibilityStore.fontSize === fontSize) {
          switchCount += 1;
        }
      });

      // 恢复原字体大小
      accessibilityStore.updateFontSize(originalFontSize);

      const passed = switchCount === fontSizes.length;

      this.addResult({
        name: '字体大小系统',
        passed,
        message: passed ? '字体大小切换正常' : '字体大小切换异常',
        details: {
          totalSizes: fontSizes.length,
          successfulSwitches: switchCount,
          currentFontSize: accessibilityStore.fontSize,
        },
      });
    } catch (error) {
      this.addResult({
        name: '字体大小系统',
        passed: false,
        message: `字体大小测试失败: ${error}`,
      });
    }
  }

  /**
   * 测试数据持久化
   */
  private async testDataPersistence(): Promise<void> {
    try {
      const testKey = 'accessibility-test';
      const testValue = { test: true, timestamp: Date.now() };

      // 测试 localStorage
      localStorage.setItem(testKey, JSON.stringify(testValue));
      const retrieved = JSON.parse(localStorage.getItem(testKey) || '{}');
      localStorage.removeItem(testKey);

      const passed = retrieved.test === testValue.test;

      this.addResult({
        name: '数据持久化',
        passed,
        message: passed ? 'localStorage 正常' : 'localStorage 异常',
        details: {
          canWrite: true,
          canRead: passed,
          testValue,
          retrieved,
        },
      });
    } catch (error) {
      this.addResult({
        name: '数据持久化',
        passed: false,
        message: `持久化测试失败: ${error}`,
      });
    }
  }

  /**
   * 测试浏览器兼容性
   */
  private testBrowserCompatibility(): void {
    const ua = navigator.userAgent.toLowerCase();
    const isWeChat = ua.includes('micromessenger');
    const isIOS = /iphone|ipad|ipod/.test(ua);
    const isAndroid = ua.includes('android');
    const isSafari = ua.includes('safari') && !ua.includes('chrome');
    const isChrome = ua.includes('chrome');

    const compatibility = {
      isWeChat,
      isIOS,
      isAndroid,
      isSafari,
      isChrome,
      speechSupported: speechManager.isApiSupported(),
      localStorageSupported: typeof Storage !== 'undefined',
    };

    let message = '浏览器兼容性检查: ';
    if (isWeChat) {
      message += '微信浏览器（语音功能可能受限）';
    } else if (isIOS && isSafari) {
      message += 'iOS Safari（语音功能正常）';
    } else if (isAndroid && isChrome) {
      message += 'Android Chrome（语音功能正常）';
    } else {
      message += '其他浏览器';
    }

    this.addResult({
      name: '浏览器兼容性',
      passed: true,
      message,
      details: compatibility,
    });
  }

  /**
   * 测试语音功能
   */
  private async testSpeechFeatures(): Promise<void> {
    if (!speechManager.isApiSupported()) {
      this.addResult({
        name: '语音功能测试',
        passed: false,
        message: '浏览器不支持语音功能',
      });
      return;
    }

    try {
      // 测试基础语音播报（不实际播放）
      const testText = '测试语音功能';
      const canSpeak = typeof speechManager.speakText === 'function';
      const canPause = typeof speechManager.pause === 'function';
      const canStop = typeof speechManager.stop === 'function';

      this.addResult({
        name: '语音功能测试',
        passed: canSpeak && canPause && canStop,
        message: canSpeak && canPause && canStop ? '语音功能接口正常' : '语音功能接口异常',
        details: {
          canSpeak,
          canPause,
          canStop,
          isSpeaking: speechManager.isSpeaking(),
          isPaused: speechManager.isPaused(),
        },
      });
    } catch (error) {
      this.addResult({
        name: '语音功能测试',
        passed: false,
        message: `语音功能测试失败: ${error}`,
      });
    }
  }

  /**
   * 添加测试结果
   */
  private addResult(result: TestResult): void {
    this.results.push(result);
    const icon = result.passed ? '✅' : '❌';
    console.log(`${icon} ${result.name}: ${result.message}`);
    if (result.details) {
      console.log('   详情:', result.details);
    }
  }

  /**
   * 打印测试结果摘要
   */
  private printResults(): void {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = Math.round((passed / total) * 100);

    console.log('\n📊 测试结果摘要:');
    console.log(`   通过: ${passed}/${total} (${percentage}%)`);

    if (passed === total) {
      console.log('🎉 所有测试通过！适老化功能运行正常。');
    } else {
      console.log('⚠️  部分测试失败，请检查相关功能。');
    }
  }

  /**
   * 获取测试结果
   */
  getResults(): TestResult[] {
    return this.results;
  }

  /**
   * 获取失败的测试
   */
  getFailedTests(): TestResult[] {
    return this.results.filter(r => !r.passed);
  }
}

// 创建测试实例
export const accessibilityTester = new AccessibilityTester();

// 导出便捷方法
export async function runAccessibilityTests(): Promise<TestResult[]> {
  return accessibilityTester.runAllTests();
}

export default accessibilityTester;
