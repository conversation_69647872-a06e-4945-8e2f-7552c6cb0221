/**
 * 语音合成工具类
 * 封装 SpeechSynthesis API，提供适老化语音功能
 */

export interface ISpeechOptions {
  text: string;
  lang?: string;
  rate?: number;
  volume?: number;
  pitch?: number;
  voice?: SpeechSynthesisVoice;
}

export interface ISpeechCallbacks {
  onStart?: () => void;
  onEnd?: () => void;
  onError?: (error: SpeechSynthesisErrorEvent) => void;
  onPause?: () => void;
  onResume?: () => void;
}

class SpeechSynthesisManager {
  private synth: SpeechSynthesis | null = null;
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private isSupported: boolean = false;
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.init();
  }

  /**
   * 初始化语音合成
   */
  private init() {
    if ('speechSynthesis' in window) {
      this.synth = window.speechSynthesis;
      this.isSupported = true;
      this.loadVoices();

      // 监听语音列表变化
      if (this.synth.onvoiceschanged !== undefined) {
        this.synth.onvoiceschanged = () => {
          this.loadVoices();
        };
      }
    } else {
      console.warn('SpeechSynthesis API is not supported in this browser');
    }
  }

  /**
   * 加载可用语音
   */
  private loadVoices() {
    if (this.synth) {
      this.voices = this.synth.getVoices();
    }
  }

  /**
   * 检查是否支持语音合成
   */
  public isApiSupported(): boolean {
    return this.isSupported;
  }

  /**
   * 获取可用语音列表
   */
  public getVoices(): SpeechSynthesisVoice[] {
    return this.voices;
  }

  /**
   * 获取中文语音
   */
  public getChineseVoices(): SpeechSynthesisVoice[] {
    return this.voices.filter(
      voice => voice.lang.includes('zh') || voice.lang.includes('cmn') || voice.name.includes('Chinese'),
    );
  }

  /**
   * 获取推荐的中文语音
   */
  public getRecommendedChineseVoice(): SpeechSynthesisVoice | null {
    const chineseVoices = this.getChineseVoices();

    // 优先选择本地语音
    const localVoice = chineseVoices.find(voice => voice.localService);
    if (localVoice) return localVoice;

    // 其次选择默认语音
    const defaultVoice = chineseVoices.find(voice => voice.default);
    if (defaultVoice) return defaultVoice;

    // 最后选择第一个可用的中文语音
    return chineseVoices[0] || null;
  }

  /**
   * 语音播报
   */
  public speak(options: ISpeechOptions, callbacks?: ISpeechCallbacks): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported || !this.synth) {
        reject(new Error('SpeechSynthesis is not supported'));
        return;
      }

      // 停止当前播放
      this.stop();

      const utterance = new SpeechSynthesisUtterance(options.text);

      // 设置语音参数
      utterance.lang = options.lang || 'zh-CN';
      utterance.rate = options.rate || 0.8; // 适老化：较慢语速
      utterance.volume = options.volume || 1.0; // 适老化：较大音量
      utterance.pitch = options.pitch || 1.0;

      // 设置语音
      if (options.voice) {
        utterance.voice = options.voice;
      } else {
        const recommendedVoice = this.getRecommendedChineseVoice();
        if (recommendedVoice) {
          utterance.voice = recommendedVoice;
        }
      }

      // 设置事件回调
      utterance.onstart = () => {
        if (callbacks && callbacks.onStart) {
          callbacks.onStart();
        }
      };

      utterance.onend = () => {
        this.currentUtterance = null;
        if (callbacks && callbacks.onEnd) {
          callbacks.onEnd();
        }
        resolve();
      };

      utterance.onerror = error => {
        this.currentUtterance = null;
        if (callbacks && callbacks.onError) {
          callbacks.onError(error);
        }
        reject(error);
      };

      utterance.onpause = () => {
        if (callbacks && callbacks.onPause) {
          callbacks.onPause();
        }
      };

      utterance.onresume = () => {
        if (callbacks && callbacks.onResume) {
          callbacks.onResume();
        }
      };

      this.currentUtterance = utterance;
      this.synth.speak(utterance);
    });
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    if (this.synth && this.synth.speaking && !this.synth.paused) {
      this.synth.pause();
    }
  }

  /**
   * 恢复播放
   */
  public resume(): void {
    if (this.synth && this.synth.paused) {
      this.synth.resume();
    }
  }

  /**
   * 停止播放
   */
  public stop(): void {
    if (this.synth) {
      this.synth.cancel();
      this.currentUtterance = null;
    }
  }

  /**
   * 检查是否正在播放
   */
  public isSpeaking(): boolean {
    return this.synth ? this.synth.speaking : false;
  }

  /**
   * 检查是否暂停
   */
  public isPaused(): boolean {
    return this.synth ? this.synth.paused : false;
  }

  /**
   * 语音播报文本（简化接口）
   */
  public speakText(text: string, options?: Partial<ISpeechOptions>): Promise<void> {
    return this.speak({
      text,
      ...options,
    });
  }

  /**
   * 语音播报元素内容
   */
  public speakElement(element: HTMLElement, options?: Partial<ISpeechOptions>): Promise<void> {
    const text = element.textContent || element.innerText || '';
    return this.speakText(text, options);
  }

  /**
   * 语音播报页面标题
   */
  public speakPageTitle(): Promise<void> {
    const title = document.title || '页面';
    return this.speakText(`当前页面：${title}`);
  }

  /**
   * 语音播报按钮操作
   */
  public speakButtonAction(buttonText: string, action: string = '按钮'): Promise<void> {
    return this.speakText(`${buttonText}${action}`);
  }

  /**
   * 语音播报表单验证错误
   */
  public speakFormError(errorMessage: string): Promise<void> {
    return this.speakText(`输入错误：${errorMessage}`);
  }

  /**
   * 语音播报成功消息
   */
  public speakSuccess(message: string): Promise<void> {
    return this.speakText(`操作成功：${message}`);
  }

  /**
   * 语音播报警告消息
   */
  public speakWarning(message: string): Promise<void> {
    return this.speakText(`注意：${message}`);
  }

  /**
   * 测试语音功能
   */
  public testSpeech(): Promise<void> {
    return this.speakText('语音功能测试，您好，欢迎使用校园移动应用的适老化功能');
  }
}

// 创建单例实例
const speechManager = new SpeechSynthesisManager();

// 导出类型和实例
export { speechManager };
export default speechManager;
