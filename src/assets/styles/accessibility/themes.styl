/**
 * 适老化主题样式
 * 基于参考设计的主题配色方案
 */

// 默认主题（保持原有样式）
.theme-default
  --accessibility-primary-color: #3DA8F5
  --accessibility-bg-color: #f5f5f5
  --accessibility-text-color: #383838
  --accessibility-card-bg: #ffffff
  --accessibility-border-color: #E8E8E8

// 深蓝白主题
.theme-dark-blue
  --accessibility-primary-color: #ffffff
  --accessibility-bg-color: #1e3a8a
  --accessibility-text-color: #ffffff
  --accessibility-card-bg: #3b82f6
  --accessibility-border-color: #60a5fa
  
  // 覆盖全局背景
  background-color: var(--accessibility-bg-color) !important
  color: var(--accessibility-text-color) !important
  
  // 卡片样式
  .van-cell, .van-card, .van-panel
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
    border-color: var(--accessibility-border-color) !important
  
  // 按钮样式
  .van-button--primary
    background-color: var(--accessibility-primary-color) !important
    border-color: var(--accessibility-primary-color) !important
    color: var(--accessibility-bg-color) !important
  
  // 导航栏
  .van-nav-bar
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
  
  // 标签栏
  .van-tabbar
    background-color: var(--accessibility-card-bg) !important
  
  .van-tabbar-item
    color: var(--accessibility-text-color) !important
    
    &--active
      color: var(--accessibility-primary-color) !important

// 巧克力色主题
.theme-chocolate
  --accessibility-primary-color: #d4af37
  --accessibility-bg-color: #3e2723
  --accessibility-text-color: #ffffff
  --accessibility-card-bg: #5d4037
  --accessibility-border-color: #8d6e63
  
  background-color: var(--accessibility-bg-color) !important
  color: var(--accessibility-text-color) !important
  
  .van-cell, .van-card, .van-panel
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
    border-color: var(--accessibility-border-color) !important
  
  .van-button--primary
    background-color: var(--accessibility-primary-color) !important
    border-color: var(--accessibility-primary-color) !important
    color: var(--accessibility-bg-color) !important
  
  .van-nav-bar
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
  
  .van-tabbar
    background-color: var(--accessibility-card-bg) !important
  
  .van-tabbar-item
    color: var(--accessibility-text-color) !important
    
    &--active
      color: var(--accessibility-primary-color) !important

// 黑底白字主题
.theme-black-white
  --accessibility-primary-color: #ffffff
  --accessibility-bg-color: #000000
  --accessibility-text-color: #ffffff
  --accessibility-card-bg: #333333
  --accessibility-border-color: #666666
  
  background-color: var(--accessibility-bg-color) !important
  color: var(--accessibility-text-color) !important
  
  .van-cell, .van-card, .van-panel
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
    border-color: var(--accessibility-border-color) !important
  
  .van-button--primary
    background-color: var(--accessibility-primary-color) !important
    border-color: var(--accessibility-primary-color) !important
    color: var(--accessibility-bg-color) !important
  
  .van-nav-bar
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
  
  .van-tabbar
    background-color: var(--accessibility-card-bg) !important
  
  .van-tabbar-item
    color: var(--accessibility-text-color) !important
    
    &--active
      color: var(--accessibility-primary-color) !important

// 森林绿主题
.theme-forest-green
  --accessibility-primary-color: #81c784
  --accessibility-bg-color: #1b5e20
  --accessibility-text-color: #ffffff
  --accessibility-card-bg: #2e7d32
  --accessibility-border-color: #4caf50
  
  background-color: var(--accessibility-bg-color) !important
  color: var(--accessibility-text-color) !important
  
  .van-cell, .van-card, .van-panel
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
    border-color: var(--accessibility-border-color) !important
  
  .van-button--primary
    background-color: var(--accessibility-primary-color) !important
    border-color: var(--accessibility-primary-color) !important
    color: var(--accessibility-bg-color) !important
  
  .van-nav-bar
    background-color: var(--accessibility-card-bg) !important
    color: var(--accessibility-text-color) !important
  
  .van-tabbar
    background-color: var(--accessibility-card-bg) !important
  
  .van-tabbar-item
    color: var(--accessibility-text-color) !important
    
    &--active
      color: var(--accessibility-primary-color) !important

// 字体大小样式
.font-size-keep-original
  font-size: 1em

.font-size-medium
  font-size: 1.1em
  
  .van-cell__title
    font-size: 1.1em !important
  
  .van-button
    font-size: 1.1em !important
  
  .van-nav-bar__title
    font-size: 1.2em !important

.font-size-large
  font-size: 1.25em
  
  .van-cell__title
    font-size: 1.25em !important
  
  .van-button
    font-size: 1.25em !important
  
  .van-nav-bar__title
    font-size: 1.4em !important
  
  // 增加行高以提高可读性
  line-height: 1.6 !important

.font-size-extra-large
  font-size: 1.5em
  
  .van-cell__title
    font-size: 1.5em !important
  
  .van-button
    font-size: 1.5em !important
    padding: 12px 20px !important
  
  .van-nav-bar__title
    font-size: 1.6em !important
  
  // 增加行高和间距
  line-height: 1.8 !important
  
  .van-cell
    padding: 16px 20px !important
  
  .van-button
    height: 56px !important

// 护眼模式
.eye-care-mode
  filter: sepia(10%) saturate(80%) brightness(95%)
  
  // 减少蓝光
  .van-button--primary
    filter: hue-rotate(15deg) saturate(90%)
  
  // 柔化白色背景
  .van-cell, .van-card, .van-panel
    background-color: #faf9f7 !important

// 高对比度增强（用于所有深色主题）
.theme-dark-blue,
.theme-chocolate,
.theme-black-white,
.theme-forest-green
  // 增强文字对比度
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.3)
  
  // 增强边框可见性
  .van-cell
    border-bottom: 1px solid var(--accessibility-border-color) !important
  
  // 增强按钮可见性
  .van-button
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3)
  
  // 增强输入框可见性
  .van-field__control
    background-color: rgba(255, 255, 255, 0.1) !important
    border: 1px solid var(--accessibility-border-color) !important
    border-radius: 4px
    padding: 8px 12px

// 适老化通用样式增强
.accessibility-enhanced
  // 增大可点击区域
  .van-cell
    min-height: 60px
    padding: 16px
  
  // 增大按钮
  .van-button
    min-height: 48px
    padding: 12px 24px
    border-radius: 8px
  
  // 增大图标
  .van-icon
    font-size: 24px !important
  
  // 增强焦点样式
  button:focus,
  .van-button:focus,
  .van-cell:focus
    outline: 3px solid var(--accessibility-primary-color)
    outline-offset: 2px

// 响应式适配
@media (max-width: 375px)
  .font-size-large,
  .font-size-extra-large
    .van-nav-bar__title
      font-size: 1.3em !important
    
    .van-button
      padding: 10px 16px !important

@media (min-width: 414px)
  .font-size-extra-large
    .van-cell
      padding: 20px !important
    
    .van-button
      height: 60px !important
      font-size: 1.6em !important
