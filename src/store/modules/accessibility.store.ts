import { getModule, Module, Mutation, Action, VuexModule } from 'vuex-module-decorators';
import store from '@/store';

// 适老化设置接口
export interface IAccessibilitySettings {
  // 主题设置
  theme: 'default' | 'dark-blue' | 'chocolate' | 'black-white' | 'forest-green';
  
  // 字体大小设置
  fontSize: 'keep-original' | 'medium' | 'large' | 'extra-large';
  
  // 语音设置
  speechEnabled: boolean;
  speechRate: number; // 语速 0.1-2.0
  speechVolume: number; // 音量 0-1
  speechPitch: number; // 音调 0-2
  
  // 护眼模式
  eyeCareMode: boolean;
}

// 主题配置
export const THEME_CONFIG = {
  'default': {
    name: '默认',
    primaryColor: '#3DA8F5',
    backgroundColor: '#f5f5f5',
    textColor: '#383838',
    cardBackground: '#ffffff',
  },
  'dark-blue': {
    name: '深蓝白',
    primaryColor: '#1976d2',
    backgroundColor: '#0d47a1',
    textColor: '#ffffff',
    cardBackground: '#1565c0',
  },
  'chocolate': {
    name: '巧克力色',
    primaryColor: '#8d6e63',
    backgroundColor: '#3e2723',
    textColor: '#ffffff',
    cardBackground: '#5d4037',
  },
  'black-white': {
    name: '黑底白字',
    primaryColor: '#ffffff',
    backgroundColor: '#000000',
    textColor: '#ffffff',
    cardBackground: '#333333',
  },
  'forest-green': {
    name: '森林绿',
    primaryColor: '#4caf50',
    backgroundColor: '#1b5e20',
    textColor: '#ffffff',
    cardBackground: '#2e7d32',
  },
};

// 字体大小配置
export const FONT_SIZE_CONFIG = {
  'keep-original': {
    name: '保持原状',
    scale: 1,
    description: '使用系统默认字体大小',
  },
  'medium': {
    name: '适中',
    scale: 1.1,
    description: '字体稍微放大，适合大部分用户',
  },
  'large': {
    name: '较大',
    scale: 1.25,
    description: '字体明显放大，便于阅读',
  },
  'extra-large': {
    name: '特大',
    scale: 1.5,
    description: '字体大幅放大，适合视力较弱的用户',
  },
};

@Module({ store, dynamic: true, namespaced: true, name: 'accessibility', stateFactory: true })
class AccessibilityStore extends VuexModule implements IAccessibilitySettings {
  // 状态
  theme: IAccessibilitySettings['theme'] = 'default';
  fontSize: IAccessibilitySettings['fontSize'] = 'keep-original';
  speechEnabled: boolean = true;
  speechRate: number = 0.8; // 适老化：较慢语速
  speechVolume: number = 1.0; // 适老化：较大音量
  speechPitch: number = 1.0; // 标准音调
  eyeCareMode: boolean = false;

  // Getters
  get currentTheme() {
    return THEME_CONFIG[this.theme];
  }

  get currentFontSize() {
    return FONT_SIZE_CONFIG[this.fontSize];
  }

  get speechSettings() {
    return {
      enabled: this.speechEnabled,
      rate: this.speechRate,
      volume: this.speechVolume,
      pitch: this.speechPitch,
    };
  }

  // Mutations
  @Mutation
  SET_THEME(theme: IAccessibilitySettings['theme']) {
    this.theme = theme;
  }

  @Mutation
  SET_FONT_SIZE(fontSize: IAccessibilitySettings['fontSize']) {
    this.fontSize = fontSize;
  }

  @Mutation
  SET_SPEECH_ENABLED(enabled: boolean) {
    this.speechEnabled = enabled;
  }

  @Mutation
  SET_SPEECH_RATE(rate: number) {
    this.speechRate = Math.max(0.1, Math.min(2.0, rate));
  }

  @Mutation
  SET_SPEECH_VOLUME(volume: number) {
    this.speechVolume = Math.max(0, Math.min(1, volume));
  }

  @Mutation
  SET_SPEECH_PITCH(pitch: number) {
    this.speechPitch = Math.max(0, Math.min(2, pitch));
  }

  @Mutation
  SET_EYE_CARE_MODE(enabled: boolean) {
    this.eyeCareMode = enabled;
  }

  @Mutation
  RESET_SETTINGS() {
    this.theme = 'default';
    this.fontSize = 'keep-original';
    this.speechEnabled = true;
    this.speechRate = 0.8;
    this.speechVolume = 1.0;
    this.speechPitch = 1.0;
    this.eyeCareMode = false;
  }

  // Actions
  @Action({ rawError: true })
  updateTheme(theme: IAccessibilitySettings['theme']) {
    this.context.commit('SET_THEME', theme);
    this.context.dispatch('applyTheme');
  }

  @Action({ rawError: true })
  updateFontSize(fontSize: IAccessibilitySettings['fontSize']) {
    this.context.commit('SET_FONT_SIZE', fontSize);
    this.context.dispatch('applyFontSize');
  }

  @Action({ rawError: true })
  updateSpeechSettings(settings: Partial<{
    enabled: boolean;
    rate: number;
    volume: number;
    pitch: number;
  }>) {
    if (settings.enabled !== undefined) {
      this.context.commit('SET_SPEECH_ENABLED', settings.enabled);
    }
    if (settings.rate !== undefined) {
      this.context.commit('SET_SPEECH_RATE', settings.rate);
    }
    if (settings.volume !== undefined) {
      this.context.commit('SET_SPEECH_VOLUME', settings.volume);
    }
    if (settings.pitch !== undefined) {
      this.context.commit('SET_SPEECH_PITCH', settings.pitch);
    }
  }

  @Action({ rawError: true })
  toggleEyeCareMode() {
    this.context.commit('SET_EYE_CARE_MODE', !this.eyeCareMode);
    this.context.dispatch('applyEyeCareMode');
  }

  @Action({ rawError: true })
  applyTheme() {
    const theme = this.currentTheme;
    const root = document.documentElement;
    
    // 应用CSS变量
    root.style.setProperty('--accessibility-primary-color', theme.primaryColor);
    root.style.setProperty('--accessibility-bg-color', theme.backgroundColor);
    root.style.setProperty('--accessibility-text-color', theme.textColor);
    root.style.setProperty('--accessibility-card-bg', theme.cardBackground);
    
    // 添加主题类名
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${this.theme}`);
  }

  @Action({ rawError: true })
  applyFontSize() {
    const fontConfig = this.currentFontSize;
    const root = document.documentElement;
    
    // 应用字体缩放
    root.style.setProperty('--accessibility-font-scale', fontConfig.scale.toString());
    
    // 添加字体大小类名
    document.body.className = document.body.className.replace(/font-size-\w+/g, '');
    document.body.classList.add(`font-size-${this.fontSize}`);
  }

  @Action({ rawError: true })
  applyEyeCareMode() {
    if (this.eyeCareMode) {
      document.body.classList.add('eye-care-mode');
    } else {
      document.body.classList.remove('eye-care-mode');
    }
  }

  @Action({ rawError: true })
  initializeSettings() {
    // 应用所有设置
    this.context.dispatch('applyTheme');
    this.context.dispatch('applyFontSize');
    this.context.dispatch('applyEyeCareMode');
  }

  @Action({ rawError: true })
  resetAllSettings() {
    this.context.commit('RESET_SETTINGS');
    this.context.dispatch('initializeSettings');
  }
}

export default getModule(AccessibilityStore);
