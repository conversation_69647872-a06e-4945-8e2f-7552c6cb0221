import Vue from 'vue';
import Vuex from 'vuex';
import createPersistedState from 'vuex-persistedstate';

Vue.use(Vuex);

// 支持的模块
type TypeModuleName = 'session' | 'welcomeFlow' | 'accessibility';

// 定义持久化插件参数
const NAMESPACE = process.env.VUE_APP_VUEX_STORAGE_KEY || '';
const persistModules: TypeModuleName[] = ['session', 'welcomeFlow', 'accessibility'];

const persistPlugin = createPersistedState({
  key: NAMESPACE,
  paths: persistModules,
});

// 获取 localStorage 存储的数据
export function getModulePersistState(moduleName: TypeModuleName): IObject {
  const state = JSON.parse(window.localStorage.getItem(NAMESPACE) || '{}');
  return state[moduleName] || {};
}

export default new Vuex.Store<any>({
  state: {},
  mutations: {},
  actions: {},
  plugins: [persistPlugin],
});
