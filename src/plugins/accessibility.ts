/**
 * 适老化功能插件
 * 在应用启动时初始化适老化设置
 */

import Vue from 'vue';
import store from '@/store';
import accessibilityStore from '@/store/modules/accessibility.store';
import speechManager from '@/utils/speechSynthesis';

// 全局混入，为所有组件添加语音播报功能
Vue.mixin({
  methods: {
    /**
     * 语音播报文本
     */
    $speak(text: string, options?: any) {
      if (!accessibilityStore.speechEnabled) {
        return Promise.resolve();
      }

      return speechManager.speakText(text, {
        rate: accessibilityStore.speechRate,
        volume: accessibilityStore.speechVolume,
        pitch: accessibilityStore.speechPitch,
        ...options,
      });
    },

    /**
     * 语音播报元素内容
     */
    $speakElement(element: HTMLElement) {
      const text = element.textContent || element.innerText || '';
      return (this as any).$speak(text.trim());
    },

    /**
     * 语音播报按钮操作
     */
    $speakButton(buttonText: string) {
      return (this as any).$speak(`${buttonText}按钮`);
    },

    /**
     * 语音播报表单错误
     */
    $speakError(errorMessage: string) {
      return (this as any).$speak(`输入错误：${errorMessage}`);
    },

    /**
     * 语音播报成功消息
     */
    $speakSuccess(message: string) {
      return (this as any).$speak(`操作成功：${message}`);
    },
  },
});

// 全局指令：点击语音播报
Vue.directive('speak', {
  bind(el: HTMLElement, binding: any) {
    const text = binding.value || el.textContent || el.innerText || '';
    
    el.addEventListener('click', () => {
      if (accessibilityStore.speechEnabled && text.trim()) {
        speechManager.speakText(text.trim(), {
          rate: accessibilityStore.speechRate,
          volume: accessibilityStore.speechVolume,
          pitch: accessibilityStore.speechPitch,
        });
      }
    });
    
    // 添加可访问性属性
    el.setAttribute('role', 'button');
    el.setAttribute('tabindex', '0');
    el.style.cursor = 'pointer';
  },
});

// 全局指令：自动语音播报（用于重要提示）
Vue.directive('auto-speak', {
  inserted(el: HTMLElement, binding: any) {
    if (!accessibilityStore.speechEnabled) return;
    
    const text = binding.value || el.textContent || el.innerText || '';
    const delay = binding.modifiers.delay ? 1000 : 0;
    
    setTimeout(() => {
      if (text.trim()) {
        speechManager.speakText(text.trim(), {
          rate: accessibilityStore.speechRate,
          volume: accessibilityStore.speechVolume,
          pitch: accessibilityStore.speechPitch,
        });
      }
    }, delay);
  },
});

// 初始化适老化功能
export function initAccessibility() {
  // 确保store已注册
  if (!store.hasModule('accessibility')) {
    console.warn('Accessibility store module not found');
    return;
  }

  // 初始化设置
  accessibilityStore.initializeSettings();

  // 监听路由变化，播报页面标题
  if (accessibilityStore.speechEnabled) {
    const router = (Vue as any).prototype.$router;
    if (router) {
      router.afterEach((to: any) => {
        setTimeout(() => {
          const title = (to.meta && to.meta.title) || document.title || '页面';
          speechManager.speakText(`进入${title}页面`);
        }, 500);
      });
    }
  }

  // 监听主题变化
  store.watch(
    () => accessibilityStore.theme,
    () => {
      accessibilityStore.applyTheme();
    }
  );

  // 监听字体大小变化
  store.watch(
    () => accessibilityStore.fontSize,
    () => {
      accessibilityStore.applyFontSize();
    }
  );

  // 监听护眼模式变化
  store.watch(
    () => accessibilityStore.eyeCareMode,
    () => {
      accessibilityStore.applyEyeCareMode();
    }
  );

  // 添加全局样式类
  document.body.classList.add('accessibility-enhanced');

  // 检查语音API支持
  if (!speechManager.isApiSupported()) {
    console.warn('SpeechSynthesis API is not supported in this browser');
  }

  console.log('Accessibility features initialized');
}

// 检测微信浏览器
export function isWeChatBrowser(): boolean {
  const ua = navigator.userAgent.toLowerCase();
  return ua.includes('micromessenger');
}

// 检测语音API支持情况
export function checkSpeechSupport(): {
  supported: boolean;
  isWeChat: boolean;
  message: string;
} {
  const isWeChat = isWeChatBrowser();
  const supported = speechManager.isApiSupported();
  
  let message = '';
  if (!supported) {
    message = '当前浏览器不支持语音功能';
  } else if (isWeChat) {
    message = '微信浏览器可能限制语音功能，建议在外部浏览器中使用';
  } else {
    message = '语音功能正常';
  }

  return {
    supported,
    isWeChat,
    message,
  };
}

// 适老化功能状态检查
export function getAccessibilityStatus() {
  return {
    theme: accessibilityStore.currentTheme,
    fontSize: accessibilityStore.currentFontSize,
    speechEnabled: accessibilityStore.speechEnabled,
    speechSupported: speechManager.isApiSupported(),
    eyeCareMode: accessibilityStore.eyeCareMode,
    isWeChatBrowser: isWeChatBrowser(),
  };
}

// 导出默认配置
export default {
  install(Vue: any) {
    // 在Vue实例上添加适老化相关方法
    Vue.prototype.$accessibility = {
      speak: speechManager.speakText.bind(speechManager),
      getStatus: getAccessibilityStatus,
      checkSupport: checkSpeechSupport,
    };
  },
};
