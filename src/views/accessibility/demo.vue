<template>
  <div class="accessibility-demo">
    <!-- 导航栏 -->
    <van-nav-bar
      title="适老化功能演示"
      left-text="返回"
      left-arrow
      @click-left="$router.go(-1)"
    />

    <!-- 内容区域 -->
    <div class="demo-content">
      <!-- 功能介绍 -->
      <van-cell-group title="功能介绍">
        <van-cell 
          title="主题切换" 
          label="提供多种高对比度主题，适合不同视力需求"
          @click="speakText('主题切换功能，提供多种高对比度主题，适合不同视力需求')"
        />
        <van-cell 
          title="字体放大" 
          label="支持特大、较大、适中等字体大小选项"
          @click="speakText('字体放大功能，支持特大、较大、适中等字体大小选项')"
        />
        <van-cell 
          title="语音播报" 
          label="点击任意内容即可语音播报，支持语速调节"
          @click="speakText('语音播报功能，点击任意内容即可语音播报，支持语速调节')"
        />
        <van-cell 
          title="护眼模式" 
          label="减少蓝光，保护视力，适合长时间使用"
          @click="speakText('护眼模式，减少蓝光，保护视力，适合长时间使用')"
        />
      </van-cell-group>

      <!-- 操作按钮 -->
      <div class="demo-actions">
        <van-button 
          type="primary" 
          block 
          @click="openSettings"
          class="action-button"
        >
          打开适老化设置
        </van-button>
        
        <van-button
          type="default"
          block
          @click="testAllFeatures"
          class="action-button"
        >
          测试所有功能
        </van-button>

        <van-button
          type="warning"
          block
          @click="runDiagnostics"
          class="action-button"
          :loading="isRunningDiagnostics"
        >
          {{ isRunningDiagnostics ? '正在检测...' : '功能诊断' }}
        </van-button>
      </div>

      <!-- 示例内容 -->
      <van-cell-group title="示例内容">
        <van-cell>
          <template #title>
            <div class="sample-content" @click="speakElement($event.target)">
              <h3>校园新闻标题</h3>
              <p>这是一段示例新闻内容，用于展示适老化功能的效果。您可以点击任意文字进行语音播报，也可以在设置中调整主题和字体大小。</p>
              <div class="news-meta">
                <span>发布时间：2025-01-23</span>
                <span>浏览次数：1324</span>
              </div>
            </div>
          </template>
        </van-cell>
      </van-cell-group>

      <!-- 当前设置状态 -->
      <van-cell-group title="当前设置">
        <van-cell 
          title="主题" 
          :value="currentThemeName"
          @click="speakText(`当前主题：${currentThemeName}`)"
        />
        <van-cell 
          title="字体大小" 
          :value="currentFontSizeName"
          @click="speakText(`当前字体大小：${currentFontSizeName}`)"
        />
        <van-cell 
          title="语音播报" 
          :value="speechEnabled ? '已开启' : '已关闭'"
          @click="speakText(`语音播报${speechEnabled ? '已开启' : '已关闭'}`)"
        />
        <van-cell 
          title="护眼模式" 
          :value="eyeCareMode ? '已开启' : '已关闭'"
          @click="speakText(`护眼模式${eyeCareMode ? '已开启' : '已关闭'}`)"
        />
      </van-cell-group>
    </div>

    <!-- 适老化按钮 -->
    <AccessibilityButton />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { namespace } from 'vuex-class';
import AccessibilityButton from '@/components/accessibility/AccessibilityButton.vue';
import speechManager from '@/utils/speechSynthesis';
import { THEME_CONFIG, FONT_SIZE_CONFIG } from '@/store/modules/accessibility.store';
import { runAccessibilityTests } from '@/utils/accessibilityTest';

const AccessibilityModule = namespace('accessibility');

@Component({
  name: 'AccessibilityDemo',
  components: {
    AccessibilityButton,
  },
})
export default class AccessibilityDemo extends Vue {
  @AccessibilityModule.State currentSettings!: any;
  @AccessibilityModule.Action initializeSettings!: () => void;

  isRunningDiagnostics: boolean = false;

  get currentThemeName() {
    const theme = this.currentSettings?.theme || 'default';
    return (THEME_CONFIG as any)[theme]?.name || '默认';
  }

  get currentFontSizeName() {
    const fontSize = this.currentSettings?.fontSize || 'keep-original';
    return (FONT_SIZE_CONFIG as any)[fontSize]?.name || '保持原状';
  }

  get speechEnabled() {
    return this.currentSettings?.speechEnabled || false;
  }

  get eyeCareMode() {
    return this.currentSettings?.eyeCareMode || false;
  }

  mounted() {
    // 初始化适老化设置
    this.initializeSettings();
    
    // 欢迎语音
    if (this.speechEnabled) {
      setTimeout(() => {
        this.speakText('欢迎使用适老化功能演示页面，您可以点击任意内容进行语音播报');
      }, 1000);
    }
  }

  async speakText(text: string) {
    if (!this.speechEnabled) {
      this.$toast('语音播报已关闭，请在设置中开启');
      return;
    }

    try {
      await speechManager.speakText(text, {
        rate: this.currentSettings.speechRate,
        volume: this.currentSettings.speechVolume,
        pitch: this.currentSettings.speechPitch,
      });
    } catch (error) {
      console.error('语音播报失败:', error);
      this.$toast('语音播报失败，请检查浏览器设置');
    }
  }

  async speakElement(element: HTMLElement) {
    const text = element.textContent || element.innerText || '';
    if (text.trim()) {
      await this.speakText(text.trim());
    }
  }

  openSettings() {
    // 这里可以触发设置面板打开
    this.speakText('正在打开适老化设置面板');
  }

  async testAllFeatures() {
    if (!this.speechEnabled) {
      this.$toast('请先开启语音播报功能');
      return;
    }

    this.$toast('开始测试所有功能');

    const tests = [
      '正在测试适老化功能',
      `当前主题：${this.currentThemeName}`,
      `当前字体大小：${this.currentFontSizeName}`,
      `语音播报：${this.speechEnabled ? '已开启' : '已关闭'}`,
      `护眼模式：${this.eyeCareMode ? '已开启' : '已关闭'}`,
      '所有功能测试完成，感谢您的使用',
    ];

    for (let i = 0; i < tests.length; i++) {
      await this.speakText(tests[i]);
      if (i < tests.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
  }

  async runDiagnostics() {
    this.isRunningDiagnostics = true;

    try {
      this.$toast('开始功能诊断...');

      const results = await runAccessibilityTests();
      const passed = results.filter(r => r.passed).length;
      const total = results.length;

      const message = `诊断完成：${passed}/${total} 项通过`;
      this.$toast(message);

      if (this.speechEnabled) {
        await this.speakText(message);

        // 播报失败的测试
        const failed = results.filter(r => !r.passed);
        if (failed.length > 0) {
          await this.speakText(`发现 ${failed.length} 项问题`);
          for (const test of failed) {
            await this.speakText(`${test.name}：${test.message}`);
          }
        } else {
          await this.speakText('所有功能正常');
        }
      }

      // 在控制台显示详细结果
      console.table(results);

    } catch (error) {
      console.error('诊断失败:', error);
      this.$toast('诊断失败，请查看控制台');
    } finally {
      this.isRunningDiagnostics = false;
    }
  }
}
</script>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

.accessibility-demo
  min-height 100vh
  background #f5f5f5

.demo-content
  padding-bottom 80px

.demo-actions
  padding 16px
  
  .action-button
    margin-bottom 12px
    height 48px
    font-size 16px

.sample-content
  cursor pointer
  
  h3
    margin 0 0 12px 0
    font-size 18px
    color #333
    line-height 1.4
  
  p
    margin 0 0 12px 0
    font-size 14px
    color #666
    line-height 1.6
  
  .news-meta
    display flex
    justify-content space-between
    font-size 12px
    color #999
    
    span
      &:hover
        color $blue

// 适老化增强样式
.accessibility-enhanced
  .sample-content
    h3
      font-size 20px
    
    p
      font-size 16px
      line-height 1.8
    
    .news-meta
      font-size 14px

// 响应式适配
@media (max-width: 375px)
  .demo-actions
    padding 12px
  
  .action-button
    height 44px
    font-size 15px
</style>
