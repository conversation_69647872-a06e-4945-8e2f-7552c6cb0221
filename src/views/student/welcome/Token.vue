<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import token<PERSON>odel, { IToken, TokenTypes } from '@/models/bpm/token';
import TemplateFormViewer from '@/components/form/TemplateFormViewer.vue';
import TemplateForm from '@/components/form/TemplateForm.vue';
import sessionStore from '@/store/modules/session.store';

@Component({
  components: {
    TemplateForm,
    TemplateFormViewer,
  },
})
export default class WelcomeToken extends Vue {
  token: IToken = {};
  formData: IObject = {};
  template: IObject[] = [];
  errors: any[] = [];
  loading: boolean = true;
  actionText: string = '提交';

  get tokenId() {
    return +this.$route.params.tokenId;
  }
  get editable() {
    return this.token.state !== 'completed' && this.token.operator_id === sessionStore.currentUser.id;
  }

  get viewable() {
    return this.token.type === TokenTypes.Approval;
  }

  mounted() {
    this.fetchData();
  }

  async fetchData() {
    try {
      this.loading = true;
      const { data } = await tokenModel.find(this.tokenId);
      this.token = data;
      this.template = data.place_form ? data.place_form.fields || [] : [];
      this.formData = data.token_payload || {};
      this.actionText = data.action_alias ? data.action_alias.submit.name : '提交';
      this.loading = false;
    } catch (error) {
      this.loading = false;
    }
  }

  submit() {
    (this.$refs.form as any).submit({
      success: (formData: IObject) => {
        this.errors = [];
        this.updateTokenPayload(formData);
      },
      fail: (errors: any) => {
        this.errors = errors;
        this.showValidateWarning();
      },
    });
  }

  async updateTokenPayload(payload: IObject) {
    await tokenModel.update({
      id: this.token.id,
      token_payload: payload,
    });
    if (this.token.state === 'processing') {
      await tokenModel.accept(this.token.id!, '');
    }
    this.$message.success('提交成功');
    this.onBack();
  }

  showValidateWarning() {
    this.$message.warning('请完善表单必填项');
  }

  onBack() {
    this.$router.back();
  }
}
</script>

<template lang="pug">
.welcome-container.info-place
  .welcome-base
    NavBar(@left-click="onBack" :title="token.name")
  .welcome-content
    .form-wrapper(v-if="editable")
      TemplateForm(
        ref="form"
        :template="template"
        :formData="formData"
        :showActions="false")
      .action
        van-button.button-lg(
          type="info"
          block
          @click="submit")
          | {{ actionText }}
    .form-wrapper(v-else-if="viewable")
      TemplateFormViewer(
        title="审批信息"
        :template="template"
        :formData="formData")
    .form-wrapper(v-else)
      // TemplateFormViewer(
      //  title="提交信息"
      //  :template="template"
      //  :formData="formData")
      //- v-if="Object.keys(formData).length > 0"
      TemplateForm.disabled(
        ref="form"
        :template="template"
        :formData="formData"
        :showActions="false")

      .action
        van-button.button-lg(
          type="info"
          block
          @click="submit")
          | 更新
</template>

<style lang="stylus" scoped>
@import '~@/assets/styles/share/studyingWelcome.styl'
@import '~@/assets/styles/global/colors.styl'

.info-place
  .welcome-content
    .form-wrapper
      margin 0 -8px
    .action
      margin-top 20px
      padding 0 16px
  .disabled
    pointer-events none
</style>
