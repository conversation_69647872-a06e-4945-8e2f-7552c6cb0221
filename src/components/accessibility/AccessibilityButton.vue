<template>
  <div class="accessibility-button">
    <!-- 悬浮按钮 -->
    <div 
      class="floating-button"
      @click="showSettings = true"
      v-if="!showSettings"
    >
      <van-icon name="setting-o" size="24px" />
      <span class="button-text">适老化</span>
    </div>

    <!-- 设置面板 -->
    <van-popup
      v-model="showSettings"
      position="bottom"
      :style="{ height: '100%' }"
      :close-on-click-overlay="false"
    >
      <AccessibilitySettings @close="showSettings = false" />
    </van-popup>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import AccessibilitySettings from './AccessibilitySettings.vue';

@Component({
  name: 'AccessibilityButton',
  components: {
    AccessibilitySettings,
  },
})
export default class AccessibilityButton extends Vue {
  showSettings: boolean = false;
}
</script>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

.accessibility-button
  .floating-button
    position fixed
    top 50%
    right 16px
    transform translateY(-50%)
    z-index 999
    background $blue
    color white
    padding 12px 8px
    border-radius 24px
    box-shadow 0 4px 12px rgba(61, 168, 245, 0.3)
    display flex
    flex-direction column
    align-items center
    gap 4px
    cursor pointer
    transition all 0.3s ease
    
    &:hover
      transform translateY(-50%) scale(1.05)
      box-shadow 0 6px 16px rgba(61, 168, 245, 0.4)
    
    .button-text
      font-size 10px
      font-weight bold
      white-space nowrap
</style>
