<template>
  <div class="accessibility-settings">
    <!-- 头部信息 -->
    <div class="settings-header">
      <div class="header-content">
        <div class="publish-info">
          <div class="publish-time">发布时间：{{ currentTime }}</div>
          <div class="view-count">浏览次数：{{ viewCount }}</div>
        </div>
        <div class="page-title">
          <div class="title-main">适老化设置</div>
          <div class="title-sub">让应用更适合您的使用习惯</div>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <!-- 设置标题 -->
      <div class="settings-title">
        <van-icon name="setting-o" />
        <span>设置</span>
        <van-icon 
          name="close" 
          class="close-icon" 
          @click="$emit('close')"
        />
      </div>

      <!-- 标签页导航 -->
      <div class="settings-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          :class="['tab-item', { active: activeTab === tab.key }]"
          @click="activeTab = tab.key"
        >
          {{ tab.name }}
        </div>
      </div>

      <!-- 主题设置 -->
      <div v-show="activeTab === 'theme'" class="tab-content">
        <div class="preview-section">
          <div class="preview-label">主题预览</div>
          <div class="theme-preview">
            <div class="preview-card" :class="`theme-${currentSettings.theme}`">
              <div class="preview-header">示例标题</div>
              <div class="preview-text">这是示例文本内容</div>
              <div class="preview-button">示例按钮</div>
            </div>
          </div>
        </div>

        <div class="options-section">
          <div 
            v-for="theme in themeOptions" 
            :key="theme.key"
            :class="['option-card', { selected: currentSettings.theme === theme.key }]"
            @click="updateTheme(theme.key)"
          >
            <div class="option-content" :style="{ backgroundColor: theme.color }">
              <span class="option-text">{{ theme.name }}</span>
              <van-icon 
                v-if="currentSettings.theme === theme.key" 
                name="success" 
                class="selected-icon"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 字体设置 -->
      <div v-show="activeTab === 'font'" class="tab-content">
        <div class="preview-section">
          <div class="preview-label">字体预览</div>
          <div class="font-preview">
            <div 
              class="preview-text-sample" 
              :class="`font-size-${currentSettings.fontSize}`"
            >
              这是字体大小预览文本，您可以根据需要选择合适的字体大小。
            </div>
          </div>
        </div>

        <div class="options-section">
          <div 
            v-for="font in fontOptions" 
            :key="font.key"
            :class="['option-card', { selected: currentSettings.fontSize === font.key }]"
            @click="updateFontSize(font.key)"
          >
            <div class="option-content">
              <span class="option-text">{{ font.name }}</span>
              <van-icon 
                v-if="currentSettings.fontSize === font.key" 
                name="success" 
                class="selected-icon"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 语音设置 -->
      <div v-show="activeTab === 'speech'" class="tab-content">
        <div class="speech-controls">
          <!-- 语音开关 -->
          <div class="control-item">
            <div class="control-label">语音播报</div>
            <van-switch 
              v-model="currentSettings.speechEnabled"
              @change="updateSpeechEnabled"
              size="24px"
            />
          </div>

          <!-- 语速调节 -->
          <div class="control-item" v-if="currentSettings.speechEnabled">
            <div class="control-label">语速调节</div>
            <div class="slider-container">
              <span class="slider-label">慢</span>
              <van-slider
                v-model="speechRate"
                :min="0.1"
                :max="2.0"
                :step="0.1"
                @change="updateSpeechRate"
                bar-height="6px"
                button-size="20px"
              />
              <span class="slider-label">快</span>
            </div>
          </div>

          <!-- 音量调节 -->
          <div class="control-item" v-if="currentSettings.speechEnabled">
            <div class="control-label">音量大小</div>
            <div class="slider-container">
              <van-icon name="volume-o" />
              <van-slider
                v-model="speechVolume"
                :min="0"
                :max="1"
                :step="0.1"
                @change="updateSpeechVolume"
                bar-height="6px"
                button-size="20px"
              />
              <van-icon name="volume" />
            </div>
          </div>

          <!-- 测试按钮 -->
          <div class="control-item" v-if="currentSettings.speechEnabled">
            <van-button 
              type="primary" 
              block 
              @click="testSpeech"
              :loading="isTesting"
            >
              {{ isTesting ? '正在播放...' : '测试语音' }}
            </van-button>
          </div>
        </div>
      </div>

      <!-- 护眼设置 -->
      <div v-show="activeTab === 'eyeCare'" class="tab-content">
        <div class="eye-care-section">
          <div class="feature-card">
            <div class="feature-icon">
              <van-icon name="eye-o" size="32px" />
            </div>
            <div class="feature-content">
              <div class="feature-title">护眼模式</div>
              <div class="feature-desc">减少蓝光，保护视力，适合长时间使用</div>
            </div>
            <div class="feature-control">
              <van-switch 
                v-model="currentSettings.eyeCareMode"
                @change="updateEyeCareMode"
                size="24px"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航 -->
    <div class="settings-footer">
      <div class="footer-nav">
        <div class="nav-item" @click="activeTab = 'theme'">
          <van-icon name="palette-o" />
          <span>主题</span>
        </div>
        <div class="nav-item" @click="testSpeech" v-if="currentSettings.speechEnabled">
          <van-icon name="pause-circle-o" />
          <span>阅读</span>
        </div>
        <div class="nav-item active">
          <van-icon name="setting-o" />
          <span>设置</span>
        </div>
        <div class="nav-item" @click="$emit('close')">
          <van-icon name="arrow-left" />
          <span>退出</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { namespace } from 'vuex-class';
import { THEME_CONFIG, FONT_SIZE_CONFIG } from '@/store/modules/accessibility.store';
import speechManager from '@/utils/speechSynthesis';

const AccessibilityModule = namespace('accessibility');

@Component({
  name: 'AccessibilitySettings',
})
export default class AccessibilitySettings extends Vue {
  @AccessibilityModule.State currentSettings!: any;
  @AccessibilityModule.Action updateTheme!: (theme: string) => void;
  @AccessibilityModule.Action updateFontSize!: (fontSize: string) => void;
  @AccessibilityModule.Action updateSpeechSettings!: (settings: any) => void;
  @AccessibilityModule.Action toggleEyeCareMode!: () => void;

  activeTab: string = 'theme';
  isTesting: boolean = false;
  speechRate: number = 0.8;
  speechVolume: number = 1.0;
  viewCount: number = 1324;

  tabs = [
    { key: 'theme', name: '主题' },
    { key: 'font', name: '字体' },
    { key: 'speech', name: '语速' },
    { key: 'eyeCare', name: '护眼' },
  ];

  themeOptions = [
    { key: 'default', name: '默认', color: '#f5f5f5' },
    { key: 'dark-blue', name: '深蓝白', color: '#1e3a8a' },
    { key: 'chocolate', name: '巧克力色', color: '#3e2723' },
    { key: 'black-white', name: '黑底白字', color: '#000000' },
    { key: 'forest-green', name: '森林绿', color: '#1b5e20' },
  ];

  fontOptions = [
    { key: 'extra-large', name: '特大' },
    { key: 'large', name: '较大' },
    { key: 'medium', name: '适中' },
    { key: 'keep-original', name: '保持原状' },
  ];

  get currentTime() {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  mounted() {
    this.speechRate = this.currentSettings.speechRate;
    this.speechVolume = this.currentSettings.speechVolume;
  }

  updateSpeechEnabled(enabled: boolean) {
    this.updateSpeechSettings({ enabled });
  }

  updateSpeechRate(rate: number) {
    this.updateSpeechSettings({ rate });
  }

  updateSpeechVolume(volume: number) {
    this.updateSpeechSettings({ volume });
  }

  updateEyeCareMode() {
    this.toggleEyeCareMode();
  }

  async testSpeech() {
    if (this.isTesting) return;

    this.isTesting = true;
    try {
      await speechManager.speakText('语音功能测试，您好，欢迎使用校园移动应用的适老化功能', {
        rate: this.speechRate,
        volume: this.speechVolume,
      });
    } catch (error) {
      console.error('语音测试失败:', error);
      this.$toast('语音功能不可用，请检查浏览器设置');
    } finally {
      this.isTesting = false;
    }
  }
}
</script>

<style lang="stylus" scoped>
@import '~@/assets/styles/global/colors'

.accessibility-settings
  position fixed
  top 0
  left 0
  right 0
  bottom 0
  z-index 1000
  background #f5f5f5
  display flex
  flex-direction column

.settings-header
  background linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)
  color white
  padding 20px 16px

  .header-content
    .publish-info
      display flex
      justify-content space-between
      font-size 12px
      opacity 0.9
      margin-bottom 16px

    .page-title
      .title-main
        font-size 20px
        font-weight bold
        margin-bottom 4px

      .title-sub
        font-size 14px
        opacity 0.8

.settings-content
  flex 1
  overflow auto
  background white
  margin 8px
  border-radius 20px 20px 0 0
  box-shadow 0 -2px 10px rgba(0, 0, 0, 0.1)

.settings-title
  display flex
  align-items center
  padding 16px 20px
  border-bottom 1px solid #f0f0f0

  .van-icon:first-child
    color $blue
    margin-right 8px

  span
    flex 1
    font-size 18px
    font-weight bold

  .close-icon
    color #999
    font-size 20px

.settings-tabs
  display flex
  background #f8f9fa
  margin 0 16px
  border-radius 8px
  padding 4px

  .tab-item
    flex 1
    text-align center
    padding 12px 8px
    border-radius 6px
    font-size 14px
    color #666
    transition all 0.3s

    &.active
      background white
      color $blue
      font-weight bold
      box-shadow 0 2px 4px rgba(0, 0, 0, 0.1)

.tab-content
  padding 20px

.preview-section
  margin-bottom 24px

  .preview-label
    font-size 16px
    font-weight bold
    margin-bottom 12px
    color #333

.theme-preview
  .preview-card
    padding 16px
    border-radius 8px
    border 1px solid #e0e0e0

    .preview-header
      font-size 16px
      font-weight bold
      margin-bottom 8px

    .preview-text
      font-size 14px
      margin-bottom 12px
      line-height 1.5

    .preview-button
      display inline-block
      padding 8px 16px
      background $blue
      color white
      border-radius 4px
      font-size 14px

.font-preview
  .preview-text-sample
    padding 16px
    border 1px solid #e0e0e0
    border-radius 8px
    line-height 1.6
    background #fafafa

.options-section
  .option-card
    margin-bottom 12px
    border-radius 8px
    overflow hidden
    border 2px solid transparent
    transition all 0.3s

    &.selected
      border-color $blue

    .option-content
      padding 16px
      display flex
      align-items center
      justify-content space-between
      background white

      .option-text
        font-size 16px
        color white
        font-weight bold

      .selected-icon
        color white
        font-size 20px

.speech-controls
  .control-item
    margin-bottom 24px

    .control-label
      font-size 16px
      font-weight bold
      margin-bottom 12px
      color #333

    .slider-container
      display flex
      align-items center
      gap 12px

      .slider-label
        font-size 12px
        color #666
        min-width 20px
        text-align center

      .van-slider
        flex 1

.eye-care-section
  .feature-card
    display flex
    align-items center
    padding 20px
    background #f8f9fa
    border-radius 12px

    .feature-icon
      margin-right 16px
      color $blue

    .feature-content
      flex 1

      .feature-title
        font-size 16px
        font-weight bold
        margin-bottom 4px
        color #333

      .feature-desc
        font-size 14px
        color #666
        line-height 1.4

.settings-footer
  background white
  border-top 1px solid #f0f0f0

  .footer-nav
    display flex
    padding 8px 0

    .nav-item
      flex 1
      display flex
      flex-direction column
      align-items center
      padding 8px
      color #666
      font-size 12px

      &.active
        color $blue

      .van-icon
        font-size 20px
        margin-bottom 4px
</style>
