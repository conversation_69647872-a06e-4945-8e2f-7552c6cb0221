import 'normalize.css';
import 'video.js/dist/video-js.css';
import 'vant/lib/index.less';
import './assets/styles/global/index.styl';
import 'babel-polyfill';

import Vue from 'vue';
import Vant from 'vant';
import tools from '@/utils/tools';
import utils from '@/utils';

import * as dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import infiniteScroll from 'vue-infinite-scroll';
import VXETable from 'vxe-table';
import message from './plugins/message';
import accessibilityPlugin, { initAccessibility } from './plugins/accessibility';
import App from './App.vue';
import router from './router';
import store from './store';
import './components/global';
import './helpers/directives';
import './helpers/filters';
import 'xe-utils';
import 'vxe-table/lib/style.css';

dayjs.extend(relativeTime);
dayjs.locale('zh-cn');

Vue.config.productionTip = false;
Vue.prototype.$tools = tools;
Vue.prototype.$utils = utils;
Vue.prototype.$dayjs = dayjs;
Vue.prototype.$message = message;

Vue.use(infiniteScroll);
Vue.use(Vant);
Vue.use(VXETable);
Vue.use(accessibilityPlugin);

new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    // 初始化适老化功能
    initAccessibility();
  },
}).$mount('#app');
